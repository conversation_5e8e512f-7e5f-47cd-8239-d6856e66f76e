#ifndef SERIAL_CONFIG_H
#define SERIAL_CONFIG_H

#include <Arduino.h>
#include <ArduinoJson.h>

class FlightController; // Forward declaration

class FlightSerialConfig {
private:
    FlightController* flightController;
    String inputBuffer;
    
    void processCommand(String command);
    void sendPIDValues();
    void setPIDValues(JsonObject& json);
    void sendSensorData();
    void sendStatus();
    void printHelp();
    
public:
    FlightSerialConfig();
    void begin();
    void setFlightController(FlightController* fc) { flightController = fc; }
    void processCommands();
};

#endif