#include "SensorManager.h"

SensorManager::SensorManager() : gpsSerial(2) {
  // Initialize sensor data
  memset(&sensorData, 0, sizeof(sensorData));
}

bool SensorManager::begin() {
  // Initialize I2C with slower clock for better reliability
  Wire.begin(21, 22);    // SDA, SCL pins for ESP32
  Wire.setClock(100000); // 100kHz instead of default 400kHz

  // Scan I2C bus to check for devices
  Serial.println("Scanning I2C bus...");
  int deviceCount = 0;
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    if (Wire.endTransmission() == 0) {
      Serial.printf("I2C device found at address 0x%02X\n", address);
      deviceCount++;
    }
  }
  Serial.printf("Found %d I2C devices\n", deviceCount);

  if (deviceCount == 0) {
    Serial.println("No I2C devices found! Check wiring.");
    return false;
  }

  // Initialize MPU6050
  if (!mpu.begin()) {
    Serial.println("Failed to find MPU6050 chip");
    mpuConnected = false;
    return false;
  } else {
    Serial.println("MPU6050 initialized successfully");
    mpuConnected = true;
    mpu.setAccelerometerRange(MPU6050_RANGE_8_G);
    mpu.setGyroRange(MPU6050_RANGE_500_DEG);
    mpu.setFilterBandwidth(MPU6050_BAND_21_HZ);
  }

  // Initialize HMC5883L
  if (!mag_sensor.begin()) {
    Serial.println(
        "Failed to find HMC5883L chip - continuing without magnetometer");
    magConnected = false;
    // Don't return false - continue without magnetometer
  } else {
    Serial.println("HMC5883L initialized successfully");
    magConnected = true;
  }

  // Initialize MS5611 - the begin() method doesn't take parameters
  if (baro_sensor.begin()) {
    if (baro_sensor.isConnected()) {
      Serial.println("MS5611 initialized successfully");
      baroConnected = true;
      // Set oversampling for better accuracy
      baro_sensor.setOversampling(OSR_ULTRA_HIGH);
    } else {
      Serial.println("MS5611 not responding");
      baroConnected = false;
    }
  } else {
    Serial.println("Failed to find MS5611 chip");
    Serial.println("Continuing without barometer...");
    baroConnected = false;
    // Don't return false - continue without barometer
  }

  // Initialize GPS
  gpsSerial.begin(9600, SERIAL_8N1, 4,
                  2); // RX, TX pins (as defined in main.cpp)

  Serial.println("All sensors initialized successfully");
  return true;
}

void SensorManager::update() {
  unsigned long currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0;
  lastUpdate = currentTime;

  // Read all sensors
  readMPU6050();
  readHMC5883L();
  readMS5611();
  readGPS();

  // Update attitude using complementary filter
  updateAttitude(dt);

  sensorData.timestamp = currentTime;
}

void SensorManager::readMPU6050() {
  sensors_event_t a, g, temp;
  mpu.getEvent(&a, &g, &temp);

  // Store accelerometer data (m/s²)
  sensorData.accel.x = a.acceleration.x;
  sensorData.accel.y = a.acceleration.y;
  sensorData.accel.z = a.acceleration.z;

  // Store gyroscope data (rad/s)
  sensorData.gyro.x = g.gyro.x;
  sensorData.gyro.y = g.gyro.y;
  sensorData.gyro.z = g.gyro.z;
}

void SensorManager::readHMC5883L() {
  if (!magConnected) {
    // Set default values if magnetometer not available
    sensorData.mag.x = 0.0;
    sensorData.mag.y = 0.0;
    sensorData.mag.z = 0.0;
    sensorData.mag.heading = 0.0;
    return;
  }

  sensors_event_t event;
  mag_sensor.getEvent(&event);

  sensorData.mag.x = event.magnetic.x;
  sensorData.mag.y = event.magnetic.y;
  sensorData.mag.z = event.magnetic.z;

  // Calculate heading
  sensorData.mag.heading =
      atan2(event.magnetic.y, event.magnetic.x) * 180.0 / PI;
  if (sensorData.mag.heading < 0) {
    sensorData.mag.heading += 360;
  }
}

void SensorManager::readMS5611() {
  // Check if MS5611 is connected before reading
  if (!baro_sensor.isConnected()) {
    // Set default values if sensor not available
    sensorData.baro.pressure = 1013.25;
    sensorData.baro.temperature = 20.0;
    sensorData.baro.altitude = 0.0;
    return;
  }

  // Read sensor with error checking
  int result = baro_sensor.read();
  if (result == MS5611_READ_OK) {
    float pressure = baro_sensor.getPressure();       // mBar
    float temperature = baro_sensor.getTemperature(); // °C

    sensorData.baro.pressure = pressure;
    sensorData.baro.temperature = temperature;

    // Calculate altitude (sea level pressure = 1013.25 mBar)
    const float seaLevelPressure = 1013.25;
    sensorData.baro.altitude =
        ((pow((seaLevelPressure / pressure), 1.0 / 5.257) - 1.0) *
         (temperature + 273.15)) /
        0.0065;
  } else {
    // Keep previous values on read error
    static unsigned long lastErrorTime = 0;
    if (millis() - lastErrorTime > 5000) { // Print error every 5 seconds
      Serial.println("MS5611 read error: " + String(result));
      lastErrorTime = millis();
    }
  }
}

void SensorManager::readGPS() {
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      if (gps.location.isValid()) {
        sensorData.gps.valid = true;
        sensorData.gps.latitude = gps.location.lat();
        sensorData.gps.longitude = gps.location.lng();
        sensorData.gps.altitude = gps.altitude.meters();
        sensorData.gps.speed = gps.speed.kmph();
        sensorData.gps.satellites = gps.satellites.value();
      } else {
        sensorData.gps.valid = false;
      }
    }
  }
}

void SensorManager::updateAttitude(float dt) {
  // Calculate angles from accelerometer
  float accelRoll = atan2(sensorData.accel.y, sensorData.accel.z) * 180.0 / PI;
  float accelPitch = atan2(-sensorData.accel.x,
                           sqrt(sensorData.accel.y * sensorData.accel.y +
                                sensorData.accel.z * sensorData.accel.z)) *
                     180.0 / PI;

  // Integrate gyroscope data
  sensorData.attitude.roll += sensorData.gyro.x * dt * 180.0 / PI;
  sensorData.attitude.pitch += sensorData.gyro.y * dt * 180.0 / PI;
  sensorData.attitude.yaw += sensorData.gyro.z * dt * 180.0 / PI;

  // Apply complementary filter
  sensorData.attitude.roll = complementaryAlpha * sensorData.attitude.roll +
                             (1.0 - complementaryAlpha) * accelRoll;
  sensorData.attitude.pitch = complementaryAlpha * sensorData.attitude.pitch +
                              (1.0 - complementaryAlpha) * accelPitch;

  // Use magnetometer for yaw correction
  sensorData.attitude.yaw = sensorData.mag.heading;
}

void SensorManager::printSensorStatus() {
  Serial.println("\n=== Sensor Status ===");
  Serial.printf("MPU6050 (Gyro/Accel): %s\n",
                mpuConnected ? "Connected" : "Disconnected");
  Serial.printf("HMC5883L (Magnetometer): %s\n",
                magConnected ? "Connected" : "Disconnected");
  Serial.printf("MS5611 (Barometer): %s\n",
                baroConnected ? "Connected" : "Disconnected");
  Serial.printf("GPS: %s\n", sensorData.gps.valid ? "Fix acquired" : "No fix");
  Serial.println("====================\n");
}