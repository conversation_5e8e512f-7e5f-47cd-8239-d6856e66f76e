#include "SerialConfig.h"
#include "../flight_control/FlightController.h"

FlightSerialConfig::FlightSerialConfig() {
    flightController = nullptr;
}

void FlightSerialConfig::begin() {
    Serial.println("\n=== ESP32 Quadcopter Configuration Interface ===");
    Serial.println("Type 'help' for available commands");
    Serial.println("===============================================\n");
}

void FlightSerialConfig::processCommands() {
    while (Serial.available()) {
        char c = Serial.read();
        
        if (c == '\n' || c == '\r') {
            if (inputBuffer.length() > 0) {
                processCommand(inputBuffer);
                inputBuffer = "";
            }
        } else {
            inputBuffer += c;
        }
    }
}

void FlightSerialConfig::processCommand(String command) {
    command.trim();
    command.toLowerCase();
    
    if (command == "help") {
        printHelp();
    }
    else if (command == "status") {
        sendStatus();
    }
    else if (command == "pid") {
        sendPIDValues();
    }
    else if (command == "sensors") {
        sendSensorData();
    }
    else if (command == "sensor_status") {
        // This would need access to sensor manager
        Serial.println("Sensor status check not implemented yet");
        Serial.println("Check startup messages for sensor initialization status");
    }
    else if (command.startsWith("set_pid")) {
        // Parse JSON PID parameters
        int jsonStart = command.indexOf('{');
        if (jsonStart != -1) {
            String jsonStr = command.substring(jsonStart);
            DynamicJsonDocument doc(1024);
            
            if (deserializeJson(doc, jsonStr) == DeserializationError::Ok) {
                JsonObject json = doc.as<JsonObject>();
                setPIDValues(json);
            } else {
                Serial.println("ERROR: Invalid JSON format");
            }
        } else {
            Serial.println("ERROR: Missing JSON parameters");
            Serial.println("Example: set_pid {\"roll\":{\"kp\":2.0,\"ki\":0.1,\"kd\":0.5}}");
        }
    }
    else if (command == "arm") {
        if (flightController) {
            flightController->arm();
            Serial.println("Flight controller ARMED via serial");
        }
    }
    else if (command == "disarm") {
        if (flightController) {
            flightController->disarm();
            Serial.println("Flight controller DISARMED via serial");
        }
    }
    else {
        Serial.println("Unknown command: " + command);
        Serial.println("Type 'help' for available commands");
    }
}

void FlightSerialConfig::printHelp() {
    Serial.println("\n=== Available Commands ===");
    Serial.println("help        - Show this help message");
    Serial.println("status      - Show flight controller status");
    Serial.println("pid         - Show current PID values");
    Serial.println("sensors     - Show sensor readings");
    Serial.println("sensor_status - Show sensor connection status");
    Serial.println("arm         - Arm the flight controller");
    Serial.println("disarm      - Disarm the flight controller");
    Serial.println("set_pid     - Set PID parameters (JSON format)");
    Serial.println("\nPID Examples:");
    Serial.println("set_pid {\"roll\":{\"kp\":2.0,\"ki\":0.1,\"kd\":0.5}}");
    Serial.println("set_pid {\"pitch\":{\"kp\":2.0,\"ki\":0.1,\"kd\":0.5}}");
    Serial.println("set_pid {\"yaw\":{\"kp\":3.0,\"ki\":0.1,\"kd\":0.0}}");
    Serial.println("set_pid {\"altitude\":{\"kp\":1.0,\"ki\":0.5,\"kd\":0.1}}");
    Serial.println("==========================\n");
}

void FlightSerialConfig::sendPIDValues() {
    if (!flightController) {
        Serial.println("ERROR: Flight controller not initialized");
        return;
    }
    
    DynamicJsonDocument doc(1024);
    
    // Roll PID
    PIDParams rollPID = flightController->getRollPID();
    doc["roll"]["kp"] = rollPID.kp;
    doc["roll"]["ki"] = rollPID.ki;
    doc["roll"]["kd"] = rollPID.kd;
    
    // Pitch PID
    PIDParams pitchPID = flightController->getPitchPID();
    doc["pitch"]["kp"] = pitchPID.kp;
    doc["pitch"]["ki"] = pitchPID.ki;
    doc["pitch"]["kd"] = pitchPID.kd;
    
    // Yaw PID
    PIDParams yawPID = flightController->getYawPID();
    doc["yaw"]["kp"] = yawPID.kp;
    doc["yaw"]["ki"] = yawPID.ki;
    doc["yaw"]["kd"] = yawPID.kd;
    
    // Altitude PID
    PIDParams altPID = flightController->getAltitudePID();
    doc["altitude"]["kp"] = altPID.kp;
    doc["altitude"]["ki"] = altPID.ki;
    doc["altitude"]["kd"] = altPID.kd;
    
    Serial.println("\n=== Current PID Values ===");
    serializeJsonPretty(doc, Serial);
    Serial.println("\n==========================\n");
}

void FlightSerialConfig::setPIDValues(JsonObject& json) {
    if (!flightController) {
        Serial.println("ERROR: Flight controller not initialized");
        return;
    }
    
    bool updated = false;
    
    if (json.containsKey("roll")) {
        JsonObject roll = json["roll"];
        if (roll.containsKey("kp") && roll.containsKey("ki") && roll.containsKey("kd")) {
            flightController->setRollPID(roll["kp"], roll["ki"], roll["kd"]);
            Serial.println("Roll PID updated");
            updated = true;
        }
    }
    
    if (json.containsKey("pitch")) {
        JsonObject pitch = json["pitch"];
        if (pitch.containsKey("kp") && pitch.containsKey("ki") && pitch.containsKey("kd")) {
            flightController->setPitchPID(pitch["kp"], pitch["ki"], pitch["kd"]);
            Serial.println("Pitch PID updated");
            updated = true;
        }
    }
    
    if (json.containsKey("yaw")) {
        JsonObject yaw = json["yaw"];
        if (yaw.containsKey("kp") && yaw.containsKey("ki") && yaw.containsKey("kd")) {
            flightController->setYawPID(yaw["kp"], yaw["ki"], yaw["kd"]);
            Serial.println("Yaw PID updated");
            updated = true;
        }
    }
    
    if (json.containsKey("altitude")) {
        JsonObject altitude = json["altitude"];
        if (altitude.containsKey("kp") && altitude.containsKey("ki") && altitude.containsKey("kd")) {
            flightController->setAltitudePID(altitude["kp"], altitude["ki"], altitude["kd"]);
            Serial.println("Altitude PID updated");
            updated = true;
        }
    }
    
    if (!updated) {
        Serial.println("ERROR: No valid PID parameters found");
    }
}

void FlightSerialConfig::sendSensorData() {
    // This would need access to sensor manager
    Serial.println("Sensor data streaming not implemented yet");
    Serial.println("Use web interface for real-time sensor monitoring");
}

void FlightSerialConfig::sendStatus() {
    if (!flightController) {
        Serial.println("ERROR: Flight controller not initialized");
        return;
    }
    
    Serial.println("\n=== Flight Controller Status ===");
    Serial.print("Armed: ");
    Serial.println(flightController->isArmed() ? "YES" : "NO");
    Serial.print("Uptime: ");
    Serial.print(millis() / 1000);
    Serial.println(" seconds");
    Serial.print("Free heap: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");
    Serial.println("================================\n");
}