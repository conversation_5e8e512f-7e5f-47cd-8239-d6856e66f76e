#include "PWMReceiver.h"

PWMReceiver* PWMReceiver::instance = nullptr;

PWMReceiver::PWMReceiver() {
    instance = this;
    armed = false;
    lastValidSignal = 0;
    
    // Initialize channel values to safe defaults
    for (int i = 0; i < 6; i++) {
        channelValues[i] = 1500; // Neutral position
        risingTime[i] = 0;
    }
    channelValues[2] = 1000; // Throttle to minimum
}

void PWMReceiver::begin(int ch1Pin, int ch2Pin, int ch3Pin, int ch4Pin, int ch5Pin, int ch6Pin) {
    pins[0] = ch1Pin;
    pins[1] = ch2Pin;
    pins[2] = ch3Pin;
    pins[3] = ch4Pin;
    pins[4] = ch5Pin;
    pins[5] = ch6Pin;
    
    // Setup pins and interrupts
    pinMode(pins[0], INPUT_PULLUP);
    pinMode(pins[1], INPUT_PULLUP);
    pinMode(pins[2], INPUT_PULLUP);
    pinMode(pins[3], INPUT_PULLUP);
    pinMode(pins[4], INPUT_PULLUP);
    pinMode(pins[5], INPUT_PULLUP);
    
    attachInterrupt(digitalPinToInterrupt(pins[0]), handleInterrupt0, CHANGE);
    attachInterrupt(digitalPinToInterrupt(pins[1]), handleInterrupt1, CHANGE);
    attachInterrupt(digitalPinToInterrupt(pins[2]), handleInterrupt2, CHANGE);
    attachInterrupt(digitalPinToInterrupt(pins[3]), handleInterrupt3, CHANGE);
    attachInterrupt(digitalPinToInterrupt(pins[4]), handleInterrupt4, CHANGE);
    attachInterrupt(digitalPinToInterrupt(pins[5]), handleInterrupt5, CHANGE);
    
    Serial.println("PWM Receiver initialized");
}

void PWMReceiver::update() {
    // Copy volatile values to struct
    noInterrupts();
    channels.roll = channelValues[0];
    channels.pitch = channelValues[1];
    channels.throttle = channelValues[2];
    channels.yaw = channelValues[3];
    channels.aux1 = channelValues[4];
    channels.aux2 = channelValues[5];
    interrupts();
    
    // Check arming conditions
    // Arm: Throttle low, Yaw right, Aux2 high
    // Disarm: Throttle low, Yaw left, or Aux2 low
    if (channels.throttle < 1100) {
        if (channels.yaw > 1800 && channels.aux2 > 1500 && !armed) {
            armed = true;
            Serial.println("ARMED");
        } else if (channels.yaw < 1200 || channels.aux2 < 1500) {
            if (armed) {
                armed = false;
                Serial.println("DISARMED");
            }
        }
    }
    
    // Check for valid signal
    if (hasValidSignal()) {
        lastValidSignal = millis();
    }
    
    checkFailsafe();
}

void PWMReceiver::handleInterrupt(int channel) {
    unsigned long currentTime = micros();
    
    if (digitalRead(pins[channel]) == HIGH) {
        // Rising edge - start timing
        risingTime[channel] = currentTime;
    } else {
        // Falling edge - calculate pulse width
        if (risingTime[channel] != 0) {
            unsigned long pulseWidth = currentTime - risingTime[channel];
            
            // Validate pulse width (typical RC range: 1000-2000µs)
            if (pulseWidth >= 800 && pulseWidth <= 2200) {
                channelValues[channel] = pulseWidth;
            }
        }
    }
}

bool PWMReceiver::hasValidSignal() {
    // Check if all channels have reasonable values
    for (int i = 0; i < 6; i++) {
        if (channelValues[i] < 900 || channelValues[i] > 2100) {
            return false;
        }
    }
    return true;
}

void PWMReceiver::checkFailsafe() {
    if (millis() - lastValidSignal > FAILSAFE_TIMEOUT) {
        // Failsafe triggered - set safe values
        armed = false;
        channelValues[0] = 1500; // Roll center
        channelValues[1] = 1500; // Pitch center
        channelValues[2] = 1000; // Throttle minimum
        channelValues[3] = 1500; // Yaw center
        channelValues[4] = 1000; // Aux1 low
        channelValues[5] = 1000; // Aux2 low
        
        Serial.println("FAILSAFE ACTIVATED");
    }
}

// Interrupt handlers
void IRAM_ATTR PWMReceiver::handleInterrupt0() {
    if (instance) instance->handleInterrupt(0);
}

void IRAM_ATTR PWMReceiver::handleInterrupt1() {
    if (instance) instance->handleInterrupt(1);
}

void IRAM_ATTR PWMReceiver::handleInterrupt2() {
    if (instance) instance->handleInterrupt(2);
}

void IRAM_ATTR PWMReceiver::handleInterrupt3() {
    if (instance) instance->handleInterrupt(3);
}

void IRAM_ATTR PWMReceiver::handleInterrupt4() {
    if (instance) instance->handleInterrupt(4);
}

void IRAM_ATTR PWMReceiver::handleInterrupt5() {
    if (instance) instance->handleInterrupt(5);
}