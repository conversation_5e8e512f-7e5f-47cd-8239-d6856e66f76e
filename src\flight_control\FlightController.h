#ifndef FLIGHT_CONTROLLER_H
#define FLIGHT_CONTROLLER_H

#include <Arduino.h>
#include <ESP32Servo.h>
#include "../sensors/SensorManager.h"
#include "../communication/PWMReceiver.h"

struct PIDParams {
    float kp;
    float ki;
    float kd;
    float maxIntegral;
    float maxOutput;
};

struct PIDController {
    PIDParams params;
    float integral;
    float lastError;
    float lastTime;
    
    float calculate(float setpoint, float input, float dt);
    void reset();
};

struct FlightParams {
    PIDParams rollPID;
    PIDParams pitchPID;
    PIDParams yawPID;
    PIDParams altitudePID;
    
    float maxRollPitchAngle;
    float maxYawRate;
    float minThrottle;
    float maxThrottle;
};

class FlightController {
private:
    Servo motorFL, motorFR, motorBL, motorBR;
    
    PIDController rollPID, pitchPID, yawPID, altitudePID;
    FlightParams params;
    
    bool armed;
    float targetAltitude;
    bool altitudeHold;
    
    void mixMotors(float throttle, float roll, float pitch, float yaw);
    void writeMotors(int fl, int fr, int bl, int br);
    
public:
    FlightController();
    void begin(int flPin, int frPin, int blPin, int brPin);
    void update(AttitudeData attitude, GyroData gyro, ChannelData channels);
    void disarm();
    void arm();
    
    // Parameter access
    void setRollPID(float kp, float ki, float kd);
    void setPitchPID(float kp, float ki, float kd);
    void setYawPID(float kp, float ki, float kd);
    void setAltitudePID(float kp, float ki, float kd);
    
    PIDParams getRollPID() { return params.rollPID; }
    PIDParams getPitchPID() { return params.pitchPID; }
    PIDParams getYawPID() { return params.yawPID; }
    PIDParams getAltitudePID() { return params.altitudePID; }
    
    bool isArmed() { return armed; }
};

#endif