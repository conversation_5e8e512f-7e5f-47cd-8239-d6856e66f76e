<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Quadcopter Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚁 ESP32 Quadcopter Dashboard</h1>
            <div class="status-bar">
                <span id="connection-status" class="status disconnected">Disconnected</span>
                <span id="uptime">Uptime: --</span>
            </div>
        </header>

        <div class="dashboard">
            <!-- Attitude Display -->
            <div class="card">
                <h3>Attitude</h3>
                <div class="attitude-display">
                    <div class="attitude-item">
                        <label>Roll:</label>
                        <span id="roll-value">0.0°</span>
                        <div class="progress-bar">
                            <div id="roll-bar" class="progress-fill"></div>
                        </div>
                    </div>
                    <div class="attitude-item">
                        <label>Pitch:</label>
                        <span id="pitch-value">0.0°</span>
                        <div class="progress-bar">
                            <div id="pitch-bar" class="progress-fill"></div>
                        </div>
                    </div>
                    <div class="attitude-item">
                        <label>Yaw:</label>
                        <span id="yaw-value">0.0°</span>
                        <div class="progress-bar">
                            <div id="yaw-bar" class="progress-fill"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gyroscope -->
            <div class="card">
                <h3>Gyroscope (rad/s)</h3>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>X:</label>
                        <span id="gyro-x">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Y:</label>
                        <span id="gyro-y">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Z:</label>
                        <span id="gyro-z">0.00</span>
                    </div>
                </div>
            </div>

            <!-- Accelerometer -->
            <div class="card">
                <h3>Accelerometer (m/s²)</h3>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>X:</label>
                        <span id="accel-x">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Y:</label>
                        <span id="accel-y">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Z:</label>
                        <span id="accel-z">0.00</span>
                    </div>
                </div>
            </div>

            <!-- Magnetometer -->
            <div class="card">
                <h3>Magnetometer</h3>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>X:</label>
                        <span id="mag-x">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Y:</label>
                        <span id="mag-y">0.00</span>
                    </div>
                    <div class="sensor-item">
                        <label>Z:</label>
                        <span id="mag-z">0.00</span>
                    </div>
                    <div class="sensor-item full-width">
                        <label>Heading:</label>
                        <span id="mag-heading">0.0°</span>
                    </div>
                </div>
            </div>

            <!-- Barometer -->
            <div class="card">
                <h3>Barometer</h3>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>Pressure:</label>
                        <span id="baro-pressure">0.00 hPa</span>
                    </div>
                    <div class="sensor-item">
                        <label>Altitude:</label>
                        <span id="baro-altitude">0.00 m</span>
                    </div>
                    <div class="sensor-item">
                        <label>Temperature:</label>
                        <span id="baro-temp">0.0°C</span>
                    </div>
                </div>
            </div>

            <!-- GPS -->
            <div class="card">
                <h3>GPS</h3>
                <div class="gps-status">
                    <div class="gps-item">
                        <label>Status:</label>
                        <span id="gps-status" class="status">No Fix</span>
                    </div>
                    <div class="gps-item">
                        <label>Satellites:</label>
                        <span id="gps-satellites">0</span>
                    </div>
                </div>
                <div class="sensor-grid">
                    <div class="sensor-item">
                        <label>Latitude:</label>
                        <span id="gps-lat">--</span>
                    </div>
                    <div class="sensor-item">
                        <label>Longitude:</label>
                        <span id="gps-lon">--</span>
                    </div>
                    <div class="sensor-item">
                        <label>Altitude:</label>
                        <span id="gps-alt">-- m</span>
                    </div>
                    <div class="sensor-item">
                        <label>Speed:</label>
                        <span id="gps-speed">0.0 km/h</span>
                    </div>
                </div>
            </div>

            <!-- Real-time Chart -->
            <div class="card chart-card">
                <h3>Real-time Attitude</h3>
                <canvas id="attitudeChart"></canvas>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>