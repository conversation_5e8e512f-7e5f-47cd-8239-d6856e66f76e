# ESP32 Quadcopter Wiring Guide

## Pin Connections

### GY-86 Module (I2C)
- VCC → 3.3V
- GND → GND  
- SDA → GPIO 21
- SCL → GPIO 22

### GPS NEO-6M
- VCC → 3.3V
- GND → GND
- RX → GPIO 2 (ESP32 TX)
- TX → GPIO 4 (ESP32 RX)

### PWM Receiver (FS TH9X)
- Channel 1 (Roll) → GPIO 32
- Channel 2 (Pitch) → GPIO 33
- Channel 3 (Throttle) → GPIO 25
- Channel 4 (Yaw) → GPIO 26
- Channel 5 (Aux1/Mode) → GPIO 27
- Channel 6 (Aux2/Arm) → GPIO 14
- VCC → 5V (from BEC)
- GND → GND

### ESCs/Motors
- Motor Front Left → GPIO 16
- Motor Front Right → GPIO 17
- Motor Back Left → GPIO 18
- Motor Back Right → GPIO 19

## Power Distribution
```
Battery (3S LiPo) → PDB → ESCs → Motors
                 ↓
                BEC (5V) → Receiver
                 ↓
                3.3V Reg → ESP32, Sensors
```

## Motor Layout (X Configuration)
```
    FL(16)     FR(17)
        \       /
         \     /
          \   /
           \ /
            X
           / \
          /   \
         /     \
        /       \
    BL(18)     BR(19)
```

## Receiver Channel Mapping
1. **Channel 1 (Roll)**: Right stick left/right
2. **Channel 2 (Pitch)**: Right stick up/down  
3. **Channel 3 (Throttle)**: Left stick up/down
4. **Channel 4 (Yaw)**: Left stick left/right
5. **Channel 5 (Aux1)**: 3-position switch (flight modes)
6. **Channel 6 (Aux2)**: 2-position switch (arm/disarm)

## Arming Sequence
1. Throttle stick to minimum
2. Yaw stick to right (>1800µs)
3. Aux2 switch to high position (>1500µs)
4. Hold for 2 seconds

## Disarming
- Throttle to minimum AND (Yaw left OR Aux2 low)

## Safety Features
- Automatic disarm on signal loss
- Failsafe activates after 1 second of no signal
- Motors stop immediately when disarmed
- Low throttle required for arming

## Calibration Steps
1. **ESC Calibration**: 
   - Set all ESCs to same timing and throttle range
   - Calibrate with transmitter (1000-2000µs)

2. **Sensor Calibration**:
   - Place quadcopter on level surface
   - Use serial interface to calibrate gyro/accel
   - Calibrate magnetometer away from metal objects

3. **PID Tuning**:
   - Start with default values
   - Use USB serial interface for real-time tuning
   - Test in small increments