#include "FlightController.h"

float PIDController::calculate(float setpoint, float input, float dt) {
    float error = setpoint - input;
    
    // Proportional term
    float proportional = params.kp * error;
    
    // Integral term
    integral += error * dt;
    integral = constrain(integral, -params.maxIntegral, params.maxIntegral);
    float integralTerm = params.ki * integral;
    
    // Derivative term
    float derivative = (error - lastError) / dt;
    float derivativeTerm = params.kd * derivative;
    
    // Calculate output
    float output = proportional + integralTerm + derivativeTerm;
    output = constrain(output, -params.maxOutput, params.maxOutput);
    
    lastError = error;
    return output;
}

void PIDController::reset() {
    integral = 0;
    lastError = 0;
}

FlightController::FlightController() {
    armed = false;
    altitudeHold = false;
    targetAltitude = 0;
    
    // Default PID parameters
    params.rollPID = {2.0, 0.1, 0.5, 100.0, 400.0};
    params.pitchPID = {2.0, 0.1, 0.5, 100.0, 400.0};
    params.yawPID = {3.0, 0.1, 0.0, 100.0, 400.0};
    params.altitudePID = {1.0, 0.5, 0.1, 100.0, 200.0};
    
    params.maxRollPitchAngle = 30.0;
    params.maxYawRate = 180.0;
    params.minThrottle = 1000;
    params.maxThrottle = 2000;
    
    // Initialize PID controllers
    rollPID.params = params.rollPID;
    pitchPID.params = params.pitchPID;
    yawPID.params = params.yawPID;
    altitudePID.params = params.altitudePID;
}

void FlightController::begin(int flPin, int frPin, int blPin, int brPin) {
    // Attach servos to motor pins
    motorFL.attach(flPin, 1000, 2000);
    motorFR.attach(frPin, 1000, 2000);
    motorBL.attach(blPin, 1000, 2000);
    motorBR.attach(brPin, 1000, 2000);
    
    // Initialize motors to minimum throttle
    writeMotors(1000, 1000, 1000, 1000);
    
    Serial.println("Flight controller initialized");
}

void FlightController::update(AttitudeData attitude, GyroData gyro, ChannelData channels) {
    if (!armed) return;
    
    static unsigned long lastTime = 0;
    unsigned long currentTime = millis();
    float dt = (currentTime - lastTime) / 1000.0;
    lastTime = currentTime;
    
    if (dt <= 0) return;
    
    // Calculate setpoints from receiver
    float rollSetpoint = map(channels.roll, 1000, 2000, -params.maxRollPitchAngle, params.maxRollPitchAngle);
    float pitchSetpoint = map(channels.pitch, 1000, 2000, -params.maxRollPitchAngle, params.maxRollPitchAngle);
    float yawRateSetpoint = map(channels.yaw, 1000, 2000, -params.maxYawRate, params.maxYawRate);
    
    // Calculate PID outputs
    float rollOutput = rollPID.calculate(rollSetpoint, attitude.roll, dt);
    float pitchOutput = pitchPID.calculate(pitchSetpoint, attitude.pitch, dt);
    float yawOutput = yawPID.calculate(yawRateSetpoint, gyro.z * 180.0 / PI, dt);
    
    // Base throttle from receiver
    float throttle = constrain(channels.throttle, params.minThrottle, params.maxThrottle);
    
    // Altitude hold (if enabled)
    if (altitudeHold && channels.aux1 > 1500) {
        // Use altitude PID to adjust throttle
        // This would need barometer or GPS altitude
        // float altitudeOutput = altitudePID.calculate(targetAltitude, currentAltitude, dt);
        // throttle += altitudeOutput;
    }
    
    // Mix outputs to motors
    mixMotors(throttle, rollOutput, pitchOutput, yawOutput);
}

void FlightController::mixMotors(float throttle, float roll, float pitch, float yaw) {
    // Quadcopter motor mixing (X configuration)
    // FL: Front Left, FR: Front Right, BL: Back Left, BR: Back Right
    
    int motorFL_val = throttle - roll + pitch - yaw;
    int motorFR_val = throttle + roll + pitch + yaw;
    int motorBL_val = throttle - roll - pitch + yaw;
    int motorBR_val = throttle + roll - pitch - yaw;
    
    // Constrain motor values
    motorFL_val = constrain(motorFL_val, params.minThrottle, params.maxThrottle);
    motorFR_val = constrain(motorFR_val, params.minThrottle, params.maxThrottle);
    motorBL_val = constrain(motorBL_val, params.minThrottle, params.maxThrottle);
    motorBR_val = constrain(motorBR_val, params.minThrottle, params.maxThrottle);
    
    writeMotors(motorFL_val, motorFR_val, motorBL_val, motorBR_val);
}

void FlightController::writeMotors(int fl, int fr, int bl, int br) {
    motorFL.writeMicroseconds(fl);
    motorFR.writeMicroseconds(fr);
    motorBL.writeMicroseconds(bl);
    motorBR.writeMicroseconds(br);
}

void FlightController::disarm() {
    armed = false;
    writeMotors(1000, 1000, 1000, 1000);
    
    // Reset PID controllers
    rollPID.reset();
    pitchPID.reset();
    yawPID.reset();
    altitudePID.reset();
}

void FlightController::arm() {
    armed = true;
    Serial.println("Flight controller ARMED");
}

void FlightController::setRollPID(float kp, float ki, float kd) {
    params.rollPID.kp = kp;
    params.rollPID.ki = ki;
    params.rollPID.kd = kd;
    rollPID.params = params.rollPID;
}

void FlightController::setPitchPID(float kp, float ki, float kd) {
    params.pitchPID.kp = kp;
    params.pitchPID.ki = ki;
    params.pitchPID.kd = kd;
    pitchPID.params = params.pitchPID;
}

void FlightController::setYawPID(float kp, float ki, float kd) {
    params.yawPID.kp = kp;
    params.yawPID.ki = ki;
    params.yawPID.kd = kd;
    yawPID.params = params.yawPID;
}

void FlightController::setAltitudePID(float kp, float ki, float kd) {
    params.altitudePID.kp = kp;
    params.altitudePID.ki = ki;
    params.altitudePID.kd = kd;
    altitudePID.params = params.altitudePID;
}