[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_speed = 921600

lib_deps = 
    adafruit/Adafruit MPU6050@^2.2.4
    adafruit/Adafruit HMC5883 Unified@^1.2.1
    robtillaart/MS5611@^0.4.1
    mikalhart/TinyGPSPlus@^1.0.3
    https://github.com/me-no-dev/ESPAsyncWebServer.git
    me-no-dev/AsyncTCP@^1.1.1
    bblanchon/Arduino<PERSON>son@^6.21.3
    madhephaestus/ESP32Servo@^0.13.0

build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM

board_build.partitions = huge_app.csv
board_build.filesystem = spiffs