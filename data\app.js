class QuadcopterDashboard {
    constructor() {
        this.ws = null;
        this.reconnectInterval = null;
        this.chart = null;
        this.chartData = {
            roll: [],
            pitch: [],
            yaw: [],
            timestamps: []
        };
        this.maxDataPoints = 50;
        
        this.init();
    }
    
    init() {
        this.setupChart();
        this.connectWebSocket();
        this.setupEventListeners();
    }
    
    setupChart() {
        const ctx = document.getElementById('attitudeChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Roll (°)',
                        data: [],
                        borderColor: '#ff6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Pitch (°)',
                        data: [],
                        borderColor: '#36a2eb',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4,
                        fill: false
                    },
                    {
                        label: 'Yaw (°)',
                        data: [],
                        borderColor: '#4bc0c0',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        beginAtZero: false,
                        min: -180,
                        max: 180
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });
    }
    
    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.updateConnectionStatus(true);
            if (this.reconnectInterval) {
                clearInterval(this.reconnectInterval);
                this.reconnectInterval = null;
            }
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.updateDashboard(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.updateConnectionStatus(false);
            this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.updateConnectionStatus(false);
        };
    }
    
    scheduleReconnect() {
        if (!this.reconnectInterval) {
            this.reconnectInterval = setInterval(() => {
                console.log('Attempting to reconnect...');
                this.connectWebSocket();
            }, 3000);
        }
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (connected) {
            statusElement.textContent = 'Connected';
            statusElement.className = 'status connected';
        } else {
            statusElement.textContent = 'Disconnected';
            statusElement.className = 'status disconnected';
        }
    }
    
    updateDashboard(data) {
        // Update attitude
        if (data.attitude) {
            this.updateAttitude(data.attitude);
            this.updateChart(data.attitude);
        }
        
        // Update gyroscope
        if (data.gyro) {
            document.getElementById('gyro-x').textContent = data.gyro.x.toFixed(3);
            document.getElementById('gyro-y').textContent = data.gyro.y.toFixed(3);
            document.getElementById('gyro-z').textContent = data.gyro.z.toFixed(3);
        }
        
        // Update accelerometer
        if (data.accel) {
            document.getElementById('accel-x').textContent = data.accel.x.toFixed(2);
            document.getElementById('accel-y').textContent = data.accel.y.toFixed(2);
            document.getElementById('accel-z').textContent = data.accel.z.toFixed(2);
        }
        
        // Update magnetometer
        if (data.mag) {
            document.getElementById('mag-x').textContent = data.mag.x.toFixed(2);
            document.getElementById('mag-y').textContent = data.mag.y.toFixed(2);
            document.getElementById('mag-z').textContent = data.mag.z.toFixed(2);
            document.getElementById('mag-heading').textContent = data.mag.heading.toFixed(1) + '°';
        }
        
        // Update barometer
        if (data.baro) {
            document.getElementById('baro-pressure').textContent = (data.baro.pressure / 100).toFixed(2) + ' hPa';
            document.getElementById('baro-altitude').textContent = data.baro.altitude.toFixed(2) + ' m';
            document.getElementById('baro-temp').textContent = data.baro.temperature.toFixed(1) + '°C';
        }
        
        // Update GPS
        if (data.gps) {
            this.updateGPS(data.gps);
        }
        
        // Update uptime
        if (data.timestamp) {
            const uptime = Math.floor(data.timestamp / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            document.getElementById('uptime').textContent = 
                `Uptime: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    updateAttitude(attitude) {
        // Update values
        document.getElementById('roll-value').textContent = attitude.roll.toFixed(1) + '°';
        document.getElementById('pitch-value').textContent = attitude.pitch.toFixed(1) + '°';
        document.getElementById('yaw-value').textContent = attitude.yaw.toFixed(1) + '°';
        
        // Update progress bars (normalize -180 to 180 degrees to 0-100%)
        const rollPercent = ((attitude.roll + 180) / 360) * 100;
        const pitchPercent = ((attitude.pitch + 180) / 360) * 100;
        const yawPercent = ((attitude.yaw + 180) / 360) * 100;
        
        document.getElementById('roll-bar').style.width = rollPercent + '%';
        document.getElementById('pitch-bar').style.width = pitchPercent + '%';
        document.getElementById('yaw-bar').style.width = yawPercent + '%';
    }
    
    updateChart(attitude) {
        const now = new Date().toLocaleTimeString();
        
        // Add new data
        this.chartData.timestamps.push(now);
        this.chartData.roll.push(attitude.roll);
        this.chartData.pitch.push(attitude.pitch);
        this.chartData.yaw.push(attitude.yaw);
        
        // Limit data points
        if (this.chartData.timestamps.length > this.maxDataPoints) {
            this.chartData.timestamps.shift();
            this.chartData.roll.shift();
            this.chartData.pitch.shift();
            this.chartData.yaw.shift();
        }
        
        // Update chart
        this.chart.data.labels = this.chartData.timestamps;
        this.chart.data.datasets[0].data = this.chartData.roll;
        this.chart.data.datasets[1].data = this.chartData.pitch;
        this.chart.data.datasets[2].data = this.chartData.yaw;
        this.chart.update('none');
    }
    
    updateGPS(gps) {
        const statusElement = document.getElementById('gps-status');
        
        if (gps.valid) {
            statusElement.textContent = 'GPS Fix';
            statusElement.className = 'status connected';
            
            document.getElementById('gps-lat').textContent = gps.latitude.toFixed(6);
            document.getElementById('gps-lon').textContent = gps.longitude.toFixed(6);
            document.getElementById('gps-alt').textContent = gps.altitude.toFixed(1) + ' m';
            document.getElementById('gps-speed').textContent = gps.speed.toFixed(1) + ' km/h';
        } else {
            statusElement.textContent = 'No Fix';
            statusElement.className = 'status disconnected';
            
            document.getElementById('gps-lat').textContent = '--';
            document.getElementById('gps-lon').textContent = '--';
            document.getElementById('gps-alt').textContent = '-- m';
            document.getElementById('gps-speed').textContent = '0.0 km/h';
        }
        
        document.getElementById('gps-satellites').textContent = gps.satellites;
    }
    
    setupEventListeners() {
        // Add any additional event listeners here
        window.addEventListener('beforeunload', () => {
            if (this.ws) {
                this.ws.close();
            }
        });
    }
    
    sendCommand(command) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(command));
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new QuadcopterDashboard();
});