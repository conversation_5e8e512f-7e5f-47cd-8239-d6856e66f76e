# ESP32 Quadcopter Flight Controller

A complete quadcopter flight controller built with ESP32, featuring:
- GY-86 10DOF sensor module (MPU6050 + HMC5883L + MS5611)
- GPS NEO-6M for position hold
- PWM receiver support for FS TH9X controller
- USB configuration interface for PID tuning
- Web UI for real-time sensor monitoring

## Hardware Components
- ESP32 DevKit
- GY-86 10DOF Module (Gyro, Accelerometer, Magnetometer, Barometer)
- GPS NEO-6M
- 4x ESCs with PWM input
- 4x Brushless motors
- FS TH9X transmitter (PWM protocol)

## Features
- Real-time flight control with PID loops
- USB serial interface for parameter tuning
- Web-based sensor dashboard
- GPS position hold capability
- Failsafe mechanisms

## Project Structure
```
├── src/
│   ├── main.cpp              # Main flight controller code
│   ├── sensors/              # Sensor drivers and data fusion
│   ├── flight_control/       # PID controllers and flight modes
│   ├── communication/        # PWM input, USB serial, WiFi
│   └── web_interface/        # Web UI files
├── lib/                      # Custom libraries
├── data/                     # SPIFFS web files
└── platformio.ini           # PlatformIO configuration
```