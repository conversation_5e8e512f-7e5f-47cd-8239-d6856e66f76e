#include <Arduino.h>
#include <WiFi.h>
#include <SPIFFS.h>
#include "sensors/SensorManager.h"
#include "flight_control/FlightController.h"
#include "communication/PWMReceiver.h"
#include "communication/SerialConfig.h"
#include "communication/WebServer.h"

// Pin definitions
#define PWM_CH1_PIN 32  // Roll
#define PWM_CH2_PIN 33  // Pitch  
#define PWM_CH3_PIN 25  // Throttle
#define PWM_CH4_PIN 26  // Yaw
#define PWM_CH5_PIN 27  // Aux1 (Flight mode)
#define PWM_CH6_PIN 14  // Aux2 (Arm/Disarm)

#define MOTOR_FL_PIN 16  // Front Left
#define MOTOR_FR_PIN 17  // Front Right
#define MOTOR_BL_PIN 18  // Back Left
#define MOTOR_BR_PIN 19  // Back Right

#define GPS_RX_PIN 4
#define GPS_TX_PIN 2

// Global objects
SensorManager sensors;
FlightController flightController;
PWMReceiver receiver;
FlightSerialConfig serialConfig;
WebServerManager webServer;

// WiFi credentials for web interface
const char* ssid = "ESP32_Quadcopter";
const char* password = "12345678";

void setup() {
    Serial.begin(115200);
    Serial.println("ESP32 Quadcopter Starting...");
    
    // Initialize SPIFFS for web files
    if (!SPIFFS.begin(true)) {
        Serial.println("SPIFFS Mount Failed");
        return;
    }
    
    // Initialize sensors
    if (!sensors.begin()) {
        Serial.println("Critical sensor initialization failed!");
        while(1) delay(100);
    }
    
    // Print sensor status
    sensors.printSensorStatus();
    
    // Initialize PWM receiver
    receiver.begin(PWM_CH1_PIN, PWM_CH2_PIN, PWM_CH3_PIN, PWM_CH4_PIN, PWM_CH5_PIN, PWM_CH6_PIN);
    
    // Initialize flight controller
    flightController.begin(MOTOR_FL_PIN, MOTOR_FR_PIN, MOTOR_BL_PIN, MOTOR_BR_PIN);
    
    // Initialize serial configuration interface
    serialConfig.begin();
    serialConfig.setFlightController(&flightController);
    
    // Setup WiFi AP for web interface
    WiFi.softAP(ssid, password);
    Serial.print("AP IP address: ");
    Serial.println(WiFi.softAPIP());
    
    // Initialize web server
    webServer.begin();
    
    Serial.println("Quadcopter initialized successfully!");
}

void loop() {
    static unsigned long lastUpdate = 0;
    unsigned long currentTime = millis();
    
    // Main control loop at 250Hz
    if (currentTime - lastUpdate >= 4) {
        lastUpdate = currentTime;
        
        // Read sensors
        sensors.update();
        
        // Read receiver inputs
        receiver.update();
        
        // Process serial commands
        serialConfig.processCommands();
        
        // Run flight control
        if (receiver.isArmed() && receiver.getThrottle() > 1100) {
            flightController.update(
                sensors.getAttitude(),
                sensors.getGyro(),
                receiver.getChannels()
            );
        } else {
            flightController.disarm();
        }
        
        // Update web interface data
        webServer.updateSensorData(sensors.getAllData());
    }
    
    // Handle web server
    webServer.handleClient();
    
    delay(1);
}