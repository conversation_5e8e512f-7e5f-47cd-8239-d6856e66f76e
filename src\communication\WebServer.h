#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <Arduino.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include "../sensors/SensorManager.h"

class WebServerManager {
private:
    AsyncWebServer server;
    AsyncWebSocket ws;
    
    SensorData currentSensorData;
    unsigned long lastDataUpdate;
    
    void handleWebSocketMessage(void *arg, uint8_t *data, size_t len);
    void onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, 
                         AwsEventType type, void *arg, uint8_t *data, size_t len);
    
    String getSensorDataJSON();
    void setupRoutes();
    
public:
    WebServerManager();
    void begin();
    void handleClient();
    void updateSensorData(SensorData data);
    void broadcastSensorData();
};

#endif