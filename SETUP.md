# ESP32 Quadcopter Setup Guide

## Prerequisites
- PlatformIO IDE (VS Code extension recommended)
- ESP32 development board
- All hardware components (see WIRING.md)

## Software Setup

### 1. Clone and Build
```bash
# Clone the project
git clone <repository-url>
cd esp32-quadcopter

# Build and upload
pio run -t upload

# Monitor serial output
pio device monitor
```

### 2. Upload Web Interface Files
```bash
# Upload SPIFFS filesystem
pio run -t uploadfs
```

## Initial Configuration

### 1. Serial Interface
Connect via USB and open serial monitor at 115200 baud:

```
=== ESP32 Quadcopter Configuration Interface ===
Type 'help' for available commands
===============================================

> help
```

### 2. Available Commands
- `status` - Show system status
- `pid` - Display current PID values  
- `sensors` - Show sensor readings
- `arm` - Arm flight controller
- `disarm` - Disarm flight controller
- `set_pid` - Update PID parameters

### 3. PID Tuning Examples
```bash
# Set Roll PID
> set_pid {"roll":{"kp":2.0,"ki":0.1,"kd":0.5}}

# Set Pitch PID  
> set_pid {"pitch":{"kp":2.0,"ki":0.1,"kd":0.5}}

# Set Yaw PID
> set_pid {"yaw":{"kp":3.0,"ki":0.1,"kd":0.0}}

# Set Altitude PID
> set_pid {"altitude":{"kp":1.0,"ki":0.5,"kd":0.1}}
```

## Web Interface Access

### 1. Connect to WiFi
The ESP32 creates an access point:
- **SSID**: `ESP32_Quadcopter`
- **Password**: `12345678`

### 2. Open Dashboard
Navigate to: `http://192.168.4.1`

The web interface shows:
- Real-time sensor data
- Attitude visualization
- GPS status
- System information

## Flight Testing

### 1. Pre-flight Checks
1. Verify all sensors are working (web interface)
2. Check receiver signal strength
3. Confirm motor directions
4. Test arming/disarming sequence
5. Verify failsafe operation

### 2. First Flight
1. Start with low PID gains
2. Test in stabilize mode only
3. Gradually increase gains if oscillation occurs
4. Monitor via web interface during flight

### 3. PID Tuning Process
1. **Start Conservative**: Use default values
2. **Increase P-gain**: Until slight oscillation appears
3. **Add D-gain**: To reduce oscillation
4. **Add I-gain**: To eliminate steady-state error
5. **Test Thoroughly**: After each change

## Troubleshooting

### Common Issues

**Sensors Not Detected**
- Check I2C wiring (SDA/SCL)
- Verify 3.3V power supply
- Check serial output for error messages

**No Receiver Signal**
- Verify PWM pin connections
- Check receiver power (5V)
- Confirm transmitter binding
- Monitor serial for channel values

**Motors Not Responding**
- Check ESC connections
- Verify ESC calibration
- Confirm arming sequence
- Check throttle range (1000-2000µs)

**Web Interface Not Loading**
- Verify WiFi connection to ESP32 AP
- Check SPIFFS upload success
- Try different browser
- Monitor serial for web server messages

### Debug Commands
```bash
# Check sensor status
> sensors

# View current PID values
> pid

# Check system status
> status
```

## Safety Warnings

⚠️ **IMPORTANT SAFETY NOTES**:
- Always remove propellers during testing
- Test all systems before first flight
- Have a kill switch ready (transmitter)
- Never fly near people or property
- Follow local drone regulations
- Keep spare parts available

## Performance Optimization

### 1. Loop Timing
- Main control loop runs at 250Hz (4ms)
- Sensor fusion uses complementary filter
- Web interface updates at 10Hz

### 2. Memory Usage
- Monitor free heap via serial interface
- Optimize data structures if needed
- Consider reducing web update rate

### 3. Battery Life
- Monitor voltage via ADC (future enhancement)
- Implement low battery warnings
- Optimize motor mixing algorithms