#include "WebServer.h"

WebServerManager::WebServerManager() : server(80), ws("/ws") {
    lastDataUpdate = 0;
}

void WebServerManager::begin() {
    // Setup WebSocket with debugging
    ws.onEvent([this](AsyncWebSocket *server, AsyncWebSocketClient *client,
                     AwsEventType type, void *arg, uint8_t *data, size_t len) {
        onWebSocketEvent(server, client, type, arg, data, len);
    });
    server.addHandler(&ws);

    setupRoutes();
    server.begin();

    Serial.println("Web server started on port 80");
    Serial.println("WebSocket endpoint: /ws");
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
}

void WebServerManager::setupRoutes() {
    // Serve static files from SPIFFS
    server.serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // API endpoints
    server.on("/api/sensors", HTTP_GET, [this](AsyncWebServerRequest *request) {
        request->send(200, "application/json", getSensorDataJSON());
    });
    
    server.on("/api/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
        DynamicJsonDocument doc(512);
        doc["uptime"] = millis();
        doc["freeHeap"] = ESP.getFreeHeap();
        doc["wifiConnected"] = WiFi.status() == WL_CONNECTED;
        doc["clientsConnected"] = ws.count();
        doc["webSocketEndpoint"] = "/ws";

        String response;
        serializeJson(doc, response);
        request->send(200, "application/json", response);
    });

    // Test endpoint to trigger WebSocket broadcast
    server.on("/api/test-ws", HTTP_GET, [this](AsyncWebServerRequest *request) {
        if (ws.count() > 0) {
            DynamicJsonDocument testDoc(256);
            testDoc["type"] = "test";
            testDoc["message"] = "WebSocket test message";
            testDoc["timestamp"] = millis();

            String testMessage;
            serializeJson(testDoc, testMessage);
            ws.textAll(testMessage);

            request->send(200, "text/plain", "Test message sent to " + String(ws.count()) + " clients");
        } else {
            request->send(200, "text/plain", "No WebSocket clients connected");
        }
    });
    
    // Handle 404
    server.onNotFound([](AsyncWebServerRequest *request) {
        request->send(404, "text/plain", "Not found");
    });
}

void WebServerManager::handleClient() {
    // Broadcast sensor data every 100ms
    if (millis() - lastDataUpdate > 100) {
        broadcastSensorData();
        lastDataUpdate = millis();
    }
    
    ws.cleanupClients();
}

void WebServerManager::updateSensorData(SensorData data) {
    currentSensorData = data;
}

void WebServerManager::broadcastSensorData() {
    if (ws.count() > 0) {
        String jsonData = getSensorDataJSON();

        // Add debugging every 5 seconds
        static unsigned long lastDebugTime = 0;
        if (millis() - lastDebugTime > 5000) {
            Serial.printf("Broadcasting to %d clients, data size: %d bytes\n",
                         ws.count(), jsonData.length());
            Serial.printf("Sample data: %s\n", jsonData.substring(0, 150).c_str());
            lastDebugTime = millis();
        }

        // Check if data is valid before sending
        if (jsonData.length() > 10) {  // Basic sanity check
            ws.textAll(jsonData);
        } else {
            Serial.println("Warning: Invalid sensor data, not broadcasting");
        }
    }
}

String WebServerManager::getSensorDataJSON() {
    DynamicJsonDocument doc(1024);
    
    // Attitude data
    doc["attitude"]["roll"] = currentSensorData.attitude.roll;
    doc["attitude"]["pitch"] = currentSensorData.attitude.pitch;
    doc["attitude"]["yaw"] = currentSensorData.attitude.yaw;
    
    // Gyro data
    doc["gyro"]["x"] = currentSensorData.gyro.x;
    doc["gyro"]["y"] = currentSensorData.gyro.y;
    doc["gyro"]["z"] = currentSensorData.gyro.z;
    
    // Accelerometer data
    doc["accel"]["x"] = currentSensorData.accel.x;
    doc["accel"]["y"] = currentSensorData.accel.y;
    doc["accel"]["z"] = currentSensorData.accel.z;
    
    // Magnetometer data
    doc["mag"]["x"] = currentSensorData.mag.x;
    doc["mag"]["y"] = currentSensorData.mag.y;
    doc["mag"]["z"] = currentSensorData.mag.z;
    doc["mag"]["heading"] = currentSensorData.mag.heading;
    
    // Barometer data
    doc["baro"]["pressure"] = currentSensorData.baro.pressure;
    doc["baro"]["altitude"] = currentSensorData.baro.altitude;
    doc["baro"]["temperature"] = currentSensorData.baro.temperature;
    
    // GPS data
    doc["gps"]["valid"] = currentSensorData.gps.valid;
    doc["gps"]["latitude"] = currentSensorData.gps.latitude;
    doc["gps"]["longitude"] = currentSensorData.gps.longitude;
    doc["gps"]["altitude"] = currentSensorData.gps.altitude;
    doc["gps"]["speed"] = currentSensorData.gps.speed;
    doc["gps"]["satellites"] = currentSensorData.gps.satellites;
    
    doc["timestamp"] = currentSensorData.timestamp;
    
    String jsonString;
    serializeJson(doc, jsonString);
    return jsonString;
}

void WebServerManager::onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client,
                                       AwsEventType type, void *arg, uint8_t *data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT: {
            Serial.printf("WebSocket client #%u connected from %s\n",
                         client->id(), client->remoteIP().toString().c_str());
            Serial.printf("Total WebSocket clients: %d\n", ws.count());
            Serial.printf("Free heap after connection: %d bytes\n", ESP.getFreeHeap());

            // Send initial sensor data immediately
            String initialData = getSensorDataJSON();
            Serial.printf("Sending initial data (%d bytes): %s\n",
                         initialData.length(), initialData.substring(0, 100).c_str());
            client->text(initialData);
            break;
        }

        case WS_EVT_DISCONNECT:
            Serial.printf("WebSocket client #%u disconnected\n", client->id());
            Serial.printf("Remaining WebSocket clients: %d\n", ws.count());
            break;

        case WS_EVT_DATA:
            Serial.printf("WebSocket data received from client #%u\n", client->id());
            handleWebSocketMessage(arg, data, len);
            break;

        case WS_EVT_PONG:
            Serial.printf("WebSocket pong from client #%u\n", client->id());
            break;

        case WS_EVT_ERROR:
            Serial.printf("WebSocket error from client #%u\n", client->id());
            break;
    }
}

void WebServerManager::handleWebSocketMessage(void *arg, uint8_t *data, size_t len) {
    AwsFrameInfo *info = (AwsFrameInfo*)arg;
    if (info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
        data[len] = 0;
        String message = (char*)data;
        
        // Handle WebSocket commands here
        Serial.println("WebSocket message: " + message);
        
        // Example: {"command": "calibrate", "sensor": "gyro"}
        DynamicJsonDocument doc(256);
        if (deserializeJson(doc, message) == DeserializationError::Ok) {
            String command = doc["command"];
            
            if (command == "ping") {
                // Respond with pong
                DynamicJsonDocument response(128);
                response["type"] = "pong";
                response["timestamp"] = millis();
                
                String responseStr;
                serializeJson(response, responseStr);
                // Send to all clients
                ws.textAll(responseStr);
            }
        }
    }
}