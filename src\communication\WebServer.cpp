#include "WebServer.h"

WebServerManager::WebServerManager() : server(80), ws("/ws") {
    lastDataUpdate = 0;
}

void WebServerManager::begin() {
    // Setup WebSocket
    ws.onEvent([this](AsyncWebSocket *server, AsyncWebSocketClient *client, 
                     AwsEventType type, void *arg, uint8_t *data, size_t len) {
        onWebSocketEvent(server, client, type, arg, data, len);
    });
    server.addHandler(&ws);
    
    setupRoutes();
    server.begin();
    
    Serial.println("Web server started on port 80");
}

void WebServerManager::setupRoutes() {
    // Serve static files from SPIFFS
    server.serveStatic("/", SPIFFS, "/").setDefaultFile("index.html");
    
    // API endpoints
    server.on("/api/sensors", HTTP_GET, [this](AsyncWebServerRequest *request) {
        request->send(200, "application/json", getSensorDataJSON());
    });
    
    server.on("/api/status", HTTP_GET, [this](AsyncWebServerRequest *request) {
        DynamicJsonDocument doc(512);
        doc["uptime"] = millis();
        doc["freeHeap"] = ESP.getFreeHeap();
        doc["wifiConnected"] = WiFi.status() == WL_CONNECTED;
        doc["clientsConnected"] = ws.count();
        
        String response;
        serializeJson(doc, response);
        request->send(200, "application/json", response);
    });
    
    // Handle 404
    server.onNotFound([](AsyncWebServerRequest *request) {
        request->send(404, "text/plain", "Not found");
    });
}

void WebServerManager::handleClient() {
    // Broadcast sensor data every 100ms
    if (millis() - lastDataUpdate > 100) {
        broadcastSensorData();
        lastDataUpdate = millis();
    }
    
    ws.cleanupClients();
}

void WebServerManager::updateSensorData(SensorData data) {
    currentSensorData = data;
}

void WebServerManager::broadcastSensorData() {
    if (ws.count() > 0) {
        String jsonData = getSensorDataJSON();
        ws.textAll(jsonData);
    }
}

String WebServerManager::getSensorDataJSON() {
    DynamicJsonDocument doc(1024);
    
    // Attitude data
    doc["attitude"]["roll"] = currentSensorData.attitude.roll;
    doc["attitude"]["pitch"] = currentSensorData.attitude.pitch;
    doc["attitude"]["yaw"] = currentSensorData.attitude.yaw;
    
    // Gyro data
    doc["gyro"]["x"] = currentSensorData.gyro.x;
    doc["gyro"]["y"] = currentSensorData.gyro.y;
    doc["gyro"]["z"] = currentSensorData.gyro.z;
    
    // Accelerometer data
    doc["accel"]["x"] = currentSensorData.accel.x;
    doc["accel"]["y"] = currentSensorData.accel.y;
    doc["accel"]["z"] = currentSensorData.accel.z;
    
    // Magnetometer data
    doc["mag"]["x"] = currentSensorData.mag.x;
    doc["mag"]["y"] = currentSensorData.mag.y;
    doc["mag"]["z"] = currentSensorData.mag.z;
    doc["mag"]["heading"] = currentSensorData.mag.heading;
    
    // Barometer data
    doc["baro"]["pressure"] = currentSensorData.baro.pressure;
    doc["baro"]["altitude"] = currentSensorData.baro.altitude;
    doc["baro"]["temperature"] = currentSensorData.baro.temperature;
    
    // GPS data
    doc["gps"]["valid"] = currentSensorData.gps.valid;
    doc["gps"]["latitude"] = currentSensorData.gps.latitude;
    doc["gps"]["longitude"] = currentSensorData.gps.longitude;
    doc["gps"]["altitude"] = currentSensorData.gps.altitude;
    doc["gps"]["speed"] = currentSensorData.gps.speed;
    doc["gps"]["satellites"] = currentSensorData.gps.satellites;
    
    doc["timestamp"] = currentSensorData.timestamp;
    
    String jsonString;
    serializeJson(doc, jsonString);
    return jsonString;
}

void WebServerManager::onWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client,
                                       AwsEventType type, void *arg, uint8_t *data, size_t len) {
    switch (type) {
        case WS_EVT_CONNECT:
            Serial.printf("WebSocket client #%u connected from %s\n", 
                         client->id(), client->remoteIP().toString().c_str());
            // Send initial sensor data
            client->text(getSensorDataJSON());
            break;
            
        case WS_EVT_DISCONNECT:
            Serial.printf("WebSocket client #%u disconnected\n", client->id());
            break;
            
        case WS_EVT_DATA:
            handleWebSocketMessage(arg, data, len);
            break;
            
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}

void WebServerManager::handleWebSocketMessage(void *arg, uint8_t *data, size_t len) {
    AwsFrameInfo *info = (AwsFrameInfo*)arg;
    if (info->final && info->index == 0 && info->len == len && info->opcode == WS_TEXT) {
        data[len] = 0;
        String message = (char*)data;
        
        // Handle WebSocket commands here
        Serial.println("WebSocket message: " + message);
        
        // Example: {"command": "calibrate", "sensor": "gyro"}
        DynamicJsonDocument doc(256);
        if (deserializeJson(doc, message) == DeserializationError::Ok) {
            String command = doc["command"];
            
            if (command == "ping") {
                // Respond with pong
                DynamicJsonDocument response(128);
                response["type"] = "pong";
                response["timestamp"] = millis();
                
                String responseStr;
                serializeJson(response, responseStr);
                // Send to all clients
                ws.textAll(responseStr);
            }
        }
    }
}