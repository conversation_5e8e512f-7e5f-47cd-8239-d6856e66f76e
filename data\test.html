<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
        #log { background: #f8f9fa; padding: 10px; height: 300px; overflow-y: scroll; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>ESP32 WebSocket Test</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <button onclick="testWebSocket()">Test WebSocket</button>
    <button onclick="testAPI()">Test API</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <h3>Log:</h3>
    <div id="log"></div>
    
    <script>
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(connected, message = '') {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.textContent = `Connected ${message}`;
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = `Disconnected ${message}`;
                statusDiv.className = 'status disconnected';
            }
        }
        
        function testWebSocket() {
            log('=== Testing WebSocket Connection ===');
            
            if (ws) {
                ws.close();
            }
            
            const wsUrl = `ws://${window.location.host}/ws`;
            log(`Connecting to: ${wsUrl}`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                log('✅ WebSocket connected successfully!');
                updateStatus(true, '(WebSocket)');
            };
            
            ws.onmessage = (event) => {
                log(`📨 Received: ${event.data.substring(0, 100)}...`);
            };
            
            ws.onclose = (event) => {
                log(`❌ WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                updateStatus(false);
            };
            
            ws.onerror = (error) => {
                log(`❌ WebSocket error: ${error}`);
                updateStatus(false, '(Error)');
            };
        }
        
        async function testAPI() {
            log('=== Testing API Endpoints ===');
            
            try {
                // Test sensors API
                const sensorsResponse = await fetch('/api/sensors');
                if (sensorsResponse.ok) {
                    const sensorsData = await sensorsResponse.json();
                    log(`✅ /api/sensors: ${JSON.stringify(sensorsData).substring(0, 100)}...`);
                } else {
                    log(`❌ /api/sensors failed: ${sensorsResponse.status}`);
                }
                
                // Test status API
                const statusResponse = await fetch('/api/status');
                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    log(`✅ /api/status: ${JSON.stringify(statusData)}`);
                } else {
                    log(`❌ /api/status failed: ${statusResponse.status}`);
                }
                
                // Test WebSocket trigger API
                const wsTestResponse = await fetch('/api/test-ws');
                if (wsTestResponse.ok) {
                    const wsTestText = await wsTestResponse.text();
                    log(`✅ /api/test-ws: ${wsTestText}`);
                } else {
                    log(`❌ /api/test-ws failed: ${wsTestResponse.status}`);
                }
                
            } catch (error) {
                log(`❌ API test error: ${error}`);
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-start WebSocket test when page loads
        window.onload = () => {
            log('Page loaded - starting automatic tests...');
            testAPI();
            setTimeout(testWebSocket, 1000);
        };
    </script>
</body>
</html>
