#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_MPU6050.h>
#include <Adafruit_HMC5883_U.h>
#include <MS5611.h>
#include <TinyGPS++.h>
#include <HardwareSerial.h>

struct AttitudeData {
    float roll;
    float pitch;
    float yaw;
};

struct GyroData {
    float x;
    float y;
    float z;
};

struct AccelData {
    float x;
    float y;
    float z;
};

struct MagData {
    float x;
    float y;
    float z;
    float heading;
};

struct BaroData {
    float pressure;
    float altitude;
    float temperature;
};

struct GPSData {
    bool valid;
    double latitude;
    double longitude;
    float altitude;
    float speed;
    int satellites;
};

struct SensorData {
    AttitudeData attitude;
    GyroData gyro;
    AccelData accel;
    MagData mag;
    BaroData baro;
    GPSData gps;
    unsigned long timestamp;
};

class SensorManager {
private:
    Adafruit_MPU6050 mpu;
    Adafruit_HMC5883_Unified mag_sensor;
    MS5611 baro_sensor;
    TinyGPSPlus gps;
    HardwareSerial gpsSerial;
    
    SensorData sensorData;
    
    // Sensor status tracking
    bool mpuConnected = false;
    bool magConnected = false;
    bool baroConnected = false;
    
    // Complementary filter variables
    float complementaryAlpha = 0.98;
    unsigned long lastUpdate = 0;
    
    void updateAttitude(float dt);
    void readMPU6050();
    void readHMC5883L();
    void readMS5611();
    void readGPS();
    
public:
    SensorManager();
    bool begin();
    void update();
    
    AttitudeData getAttitude() { return sensorData.attitude; }
    GyroData getGyro() { return sensorData.gyro; }
    AccelData getAccel() { return sensorData.accel; }
    MagData getMag() { return sensorData.mag; }
    BaroData getBaro() { return sensorData.baro; }
    GPSData getGPS() { return sensorData.gps; }
    SensorData getAllData() { return sensorData; }
    
    // Sensor status methods
    bool isMPUConnected() { return mpuConnected; }
    bool isMagConnected() { return magConnected; }
    bool isBaroConnected() { return baroConnected; }
    void printSensorStatus();
};

#endif