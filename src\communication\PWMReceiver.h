#ifndef PWM_RECEIVER_H
#define PWM_RECEIVER_H

#include <Arduino.h>

struct ChannelData {
    int roll;      // Channel 1
    int pitch;     // Channel 2  
    int throttle;  // Channel 3
    int yaw;       // Channel 4
    int aux1;      // Channel 5 (Flight mode)
    int aux2;      // Channel 6 (Arm/Disarm)
};

class PWMReceiver {
private:
    int pins[6];
    volatile unsigned long risingTime[6];
    volatile int channelValues[6];
    ChannelData channels;
    
    bool armed;
    unsigned long lastValidSignal;
    static const unsigned long FAILSAFE_TIMEOUT = 1000; // 1 second
    
    static void IRAM_ATTR handleInterrupt0();
    static void IRAM_ATTR handleInterrupt1();
    static void IRAM_ATTR handleInterrupt2();
    static void IRAM_ATTR handleInterrupt3();
    static void IRAM_ATTR handleInterrupt4();
    static void IRAM_ATTR handleInterrupt5();
    
    static PWMReceiver* instance;
    void handleInterrupt(int channel);
    
public:
    PWMReceiver();
    void begin(int ch1Pin, int ch2Pin, int ch3Pin, int ch4Pin, int ch5Pin, int ch6Pin);
    void update();
    
    ChannelData getChannels() { return channels; }
    int getRoll() { return channels.roll; }
    int getPitch() { return channels.pitch; }
    int getThrottle() { return channels.throttle; }
    int getYaw() { return channels.yaw; }
    int getAux1() { return channels.aux1; }
    int getAux2() { return channels.aux2; }
    
    bool isArmed() { return armed; }
    bool hasValidSignal();
    void checkFailsafe();
};

#endif