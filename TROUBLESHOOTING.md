# ESP32 Quadcopter Troubleshooting Guide

## I2C Communication Errors

### Symptoms
```
[289187][E][Wire.cpp:499] requestFrom(): i2cWriteReadNonStop returned Error -1
```

### Common Causes & Solutions

#### 1. Wiring Issues
**Check connections:**
- SDA → GPIO 21
- SCL → GPIO 22  
- VCC → 3.3V (NOT 5V for most sensors)
- GND → GND

**Common mistakes:**
- Using 5V instead of 3.3V
- Loose connections
- Wrong pin assignments
- Missing pull-up resistors (usually internal ones are sufficient)

#### 2. Power Supply Issues
- Ensure stable 3.3V supply
- Check if ESP32 can provide enough current
- Use external 3.3V regulator if needed
- Verify ground connections

#### 3. I2C Address Conflicts
**GY-86 Module typical addresses:**
- MPU6050: 0x68 or 0x69
- HMC5883L: 0x1E
- MS5611: 0x77 or 0x76

**Debug steps:**
1. Run I2C scanner (built into our code)
2. Check which devices are detected
3. Verify expected addresses

#### 4. Clock Speed Issues
Our code now uses 100kHz instead of 400kHz for better reliability:
```cpp
Wire.setClock(100000); // 100kHz
```

#### 5. Sensor Module Quality
Some cheap GY-86 modules have:
- Poor soldering
- Faulty sensors
- Wrong component values

### Debugging Steps

#### 1. Check Serial Output
```
ESP32 Quadcopter Starting...
Scanning I2C bus...
I2C device found at address 0x68
I2C device found at address 0x1E
I2C device found at address 0x77
Found 3 I2C devices
MPU6050 initialized successfully
HMC5883L initialized successfully
MS5611 found at address 0x77
MS5611 initialized successfully
```

#### 2. Use Serial Commands
```
> sensor_status
```

#### 3. Individual Sensor Testing
Test each sensor separately by commenting out others in the code.

### Specific Sensor Issues

#### MPU6050 (Gyro/Accelerometer)
- **Critical sensor** - system won't start without it
- Usually most reliable on GY-86 modules
- If failing, check power and I2C connections first

#### HMC5883L (Magnetometer)
- **Non-critical** - system continues without it
- Often problematic on cheap modules
- Can be disabled if causing issues
- Alternative: Use GPS heading for yaw

#### MS5611 (Barometer)
- **Non-critical** - system continues without it
- Two possible I2C addresses (0x76, 0x77)
- Our code tries both addresses automatically
- Can be disabled for basic flight testing

### Hardware Alternatives

#### If GY-86 Module Fails
Use individual sensor breakouts:
- MPU6050 breakout (essential)
- BMP280 instead of MS5611 (barometer)
- Skip magnetometer initially

#### Wiring Individual Sensors
```
MPU6050:
- VCC → 3.3V
- GND → GND
- SDA → GPIO 21
- SCL → GPIO 22

BMP280 (alternative barometer):
- VCC → 3.3V
- GND → GND
- SDA → GPIO 21
- SCL → GPIO 22
```

### Code Modifications for Testing

#### Disable Problematic Sensors
In `SensorManager.cpp`, comment out problematic sensors:

```cpp
// Skip magnetometer initialization
/*
if (!mag_sensor.begin()) {
    Serial.println("Failed to find HMC5883L chip");
    magConnected = false;
} else {
    Serial.println("HMC5883L initialized successfully");
    magConnected = true;
}
*/
magConnected = false; // Force disable
```

#### Test with Minimal Sensors
For initial testing, you only need:
1. MPU6050 (gyro/accel) - **Required**
2. GPS (optional but recommended)

Barometer and magnetometer can be added later.

### Prevention Tips

1. **Use quality modules** - Avoid the cheapest GY-86 modules
2. **Check power supply** - Use multimeter to verify 3.3V
3. **Short wires** - Keep I2C wires as short as possible
4. **Proper grounding** - Ensure good ground connections
5. **Test incrementally** - Add one sensor at a time

### Emergency Flight Mode

If sensors keep failing, you can modify the code for manual-only flight:
- Disable attitude stabilization
- Use direct PWM pass-through to motors
- Manual control only (no auto-level)

This requires significant code changes but allows basic flight testing while debugging sensor issues.

## Other Common Issues

### GPS Not Getting Fix
- Move away from buildings/interference
- Wait 5-10 minutes for cold start
- Check antenna connection
- Verify baud rate (9600 for NEO-6M)

### PWM Receiver Issues
- Check 5V power to receiver
- Verify transmitter binding
- Test individual channels
- Check PWM pin assignments

### Motor/ESC Problems
- Verify ESC calibration
- Check motor direction
- Test individual motors
- Confirm PWM signal range (1000-2000µs)

### Web Interface Not Loading
- Check WiFi AP connection
- Verify SPIFFS upload
- Try different browser
- Check serial output for errors