* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    font-size: 1.1em;
}

.status {
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
}

.status.connected {
    background-color: #4CAF50;
    color: white;
}

.status.disconnected {
    background-color: #f44336;
    color: white;
}

.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
}

.sensor-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.sensor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.sensor-item.full-width {
    grid-column: 1 / -1;
}

.sensor-item label {
    font-weight: 600;
    color: #4a5568;
}

.sensor-item span {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #2d3748;
}

.attitude-display {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.attitude-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.attitude-item label {
    min-width: 60px;
    font-weight: 600;
    color: #4a5568;
}

.attitude-item span {
    min-width: 80px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #2d3748;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.gps-status {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px;
    background: #f7fafc;
    border-radius: 8px;
}

.gps-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.gps-item label {
    font-weight: 600;
    color: #4a5568;
}

.chart-card {
    grid-column: 1 / -1;
}

#attitudeChart {
    max-height: 300px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .dashboard {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .sensor-grid {
        grid-template-columns: 1fr;
    }
    
    .attitude-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .attitude-item span {
        text-align: center;
    }
}

/* Animation for data updates */
.sensor-item.updated {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}